2025-06-13 13:51:27 [main] INFO  com.converter.ConverterApplication - Starting ConverterApplication using Java ******** on YOS-9KL7KG1Q6NB with PID 33540 (E:\SqlservertoKingBase\ConvertBySplit\target\classes started by Administrator in E:\SqlservertoKingBase\ConvertBySplit)
2025-06-13 13:51:27 [main] DEBUG com.converter.ConverterApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-13 13:51:27 [main] INFO  com.converter.ConverterApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-13 13:51:28 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-13 13:51:28 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-13 13:51:28 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-13 13:51:28 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-13 13:51:28 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 884 ms
2025-06-13 13:51:28 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-13 13:51:28 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-13 13:51:28 [main] INFO  com.converter.ConverterApplication - Started ConverterApplication in 1.75 seconds (JVM running for 2.411)
2025-06-13 13:52:10 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 13:52:10 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-13 13:52:10 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-06-13 14:12:04 [main] INFO  com.converter.ConverterApplication - Starting ConverterApplication using Java ******** on YOS-9KL7KG1Q6NB with PID 18180 (E:\SqlservertoKingBase\ConvertBySplit\target\classes started by Administrator in E:\SqlservertoKingBase\ConvertBySplit)
2025-06-13 14:12:04 [main] DEBUG com.converter.ConverterApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-13 14:12:04 [main] INFO  com.converter.ConverterApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-13 14:12:05 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-13 14:12:05 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-13 14:12:05 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-13 14:12:05 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-13 14:12:05 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 737 ms
2025-06-13 14:12:05 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-13 14:12:05 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-13 14:12:05 [main] INFO  com.converter.ConverterApplication - Started ConverterApplication in 1.476 seconds (JVM running for 1.972)
2025-06-13 14:12:07 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 14:12:07 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-13 14:12:07 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-06-13 14:12:43 [http-nio-8080-exec-2] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 14:12:43 [http-nio-8080-exec-2] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 14:12:43 [http-nio-8080-exec-2] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 14:12:43 [http-nio-8080-exec-2] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 14:12:43 [http-nio-8080-exec-2] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 14:12:43 [http-nio-8080-exec-2] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 14:12:43 [http-nio-8080-exec-2] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 14:12:43 [http-nio-8080-exec-2] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 14:12:43 [http-nio-8080-exec-2] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 14:12:43 [http-nio-8080-exec-2] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 14:12:43 [http-nio-8080-exec-2] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 14:12:43 [http-nio-8080-exec-2] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 14:12:43 [http-nio-8080-exec-2] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 14:17:12 [main] INFO  com.converter.ConverterApplication - Starting ConverterApplication using Java ******** on YOS-9KL7KG1Q6NB with PID 22700 (E:\SqlservertoKingBase\ConvertBySplit\target\classes started by Administrator in E:\SqlservertoKingBase\ConvertBySplit)
2025-06-13 14:17:12 [main] DEBUG com.converter.ConverterApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-13 14:17:12 [main] INFO  com.converter.ConverterApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-13 14:17:13 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-13 14:17:13 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-13 14:17:13 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-13 14:17:13 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-13 14:17:13 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 720 ms
2025-06-13 14:17:13 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-13 14:17:13 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-13 14:17:13 [main] INFO  com.converter.ConverterApplication - Started ConverterApplication in 1.455 seconds (JVM running for 1.96)
2025-06-13 14:17:16 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 14:17:16 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-13 14:17:16 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-13 14:17:16 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 14:17:16 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 14:17:16 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 14:17:16 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 14:17:16 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 14:17:16 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 14:17:16 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 14:17:16 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 14:17:16 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 14:17:16 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 14:17:16 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 14:17:16 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 14:17:16 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 14:17:19 [http-nio-8080-exec-2] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 14:17:19 [http-nio-8080-exec-2] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 14:17:19 [http-nio-8080-exec-2] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 14:17:19 [http-nio-8080-exec-2] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 14:17:19 [http-nio-8080-exec-2] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 14:17:19 [http-nio-8080-exec-2] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 14:17:19 [http-nio-8080-exec-2] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 14:17:19 [http-nio-8080-exec-2] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 14:17:19 [http-nio-8080-exec-2] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 14:17:19 [http-nio-8080-exec-2] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 14:17:19 [http-nio-8080-exec-2] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 14:17:19 [http-nio-8080-exec-2] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 14:17:19 [http-nio-8080-exec-2] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 14:17:22 [http-nio-8080-exec-3] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 14:17:22 [http-nio-8080-exec-3] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 14:17:22 [http-nio-8080-exec-3] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 14:17:22 [http-nio-8080-exec-3] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 14:17:22 [http-nio-8080-exec-3] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 14:17:22 [http-nio-8080-exec-3] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 14:17:22 [http-nio-8080-exec-3] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 14:17:22 [http-nio-8080-exec-3] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 14:17:22 [http-nio-8080-exec-3] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 14:17:22 [http-nio-8080-exec-3] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 14:17:22 [http-nio-8080-exec-3] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 14:17:22 [http-nio-8080-exec-3] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 14:17:22 [http-nio-8080-exec-3] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 14:17:34 [main] INFO  com.converter.ConverterApplication - Starting ConverterApplication using Java ******** on YOS-9KL7KG1Q6NB with PID 32200 (E:\SqlservertoKingBase\ConvertBySplit\target\classes started by Administrator in E:\SqlservertoKingBase\ConvertBySplit)
2025-06-13 14:17:34 [main] DEBUG com.converter.ConverterApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-13 14:17:34 [main] INFO  com.converter.ConverterApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-13 14:17:35 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-13 14:17:35 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-13 14:17:35 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-13 14:17:35 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-13 14:17:35 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 714 ms
2025-06-13 14:17:35 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-13 14:17:35 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-13 14:17:35 [main] INFO  com.converter.ConverterApplication - Started ConverterApplication in 1.524 seconds (JVM running for 2.248)
2025-06-13 14:17:40 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 14:17:40 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-13 14:17:40 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-13 14:17:40 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 14:17:40 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 14:17:40 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 14:17:40 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 14:17:40 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 14:17:40 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 14:17:40 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 14:17:40 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 14:17:40 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 14:17:40 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 14:17:40 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 14:17:40 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 14:17:40 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 14:26:33 [main] INFO  com.converter.ConverterApplication - Starting ConverterApplication using Java ******** on YOS-9KL7KG1Q6NB with PID 34204 (E:\SqlservertoKingBase\ConvertBySplit\target\classes started by Administrator in E:\SqlservertoKingBase\ConvertBySplit)
2025-06-13 14:26:33 [main] DEBUG com.converter.ConverterApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-13 14:26:33 [main] INFO  com.converter.ConverterApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-13 14:26:34 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-13 14:26:34 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-13 14:26:34 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-13 14:26:34 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-13 14:26:34 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 740 ms
2025-06-13 14:26:34 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-13 14:26:35 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-13 14:26:35 [main] INFO  com.converter.ConverterApplication - Started ConverterApplication in 1.473 seconds (JVM running for 1.944)
2025-06-13 14:26:38 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 14:26:38 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-13 14:26:38 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-06-13 14:26:39 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 14:26:39 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 14:26:39 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 14:26:39 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 14:26:39 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 14:26:39 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 14:26:39 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 14:26:39 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 14:26:39 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 14:26:39 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 14:26:39 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 14:26:39 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 14:26:39 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 14:28:54 [main] INFO  com.converter.ConverterApplication - Starting ConverterApplication using Java ******** on YOS-9KL7KG1Q6NB with PID 26064 (E:\SqlservertoKingBase\ConvertBySplit\target\classes started by Administrator in E:\SqlservertoKingBase\ConvertBySplit)
2025-06-13 14:28:54 [main] DEBUG com.converter.ConverterApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-13 14:28:54 [main] INFO  com.converter.ConverterApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-13 14:28:54 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-13 14:28:54 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-13 14:28:54 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-13 14:28:55 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-13 14:28:55 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 812 ms
2025-06-13 14:28:55 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-13 14:28:55 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-13 14:28:55 [main] INFO  com.converter.ConverterApplication - Started ConverterApplication in 1.641 seconds (JVM running for 2.144)
2025-06-13 14:28:56 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 14:28:56 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-13 14:28:56 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-13 14:28:56 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 14:28:56 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 14:28:56 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 14:28:56 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 14:28:56 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 14:28:56 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 14:28:56 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 14:28:56 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 14:28:56 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 14:28:56 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 14:28:56 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 14:28:56 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 14:28:56 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
2025-06-13 14:32:24 [main] INFO  com.converter.ConverterApplication - Starting ConverterApplication using Java ******** on YOS-9KL7KG1Q6NB with PID 29016 (E:\SqlservertoKingBase\ConvertBySplit\target\classes started by Administrator in E:\SqlservertoKingBase\ConvertBySplit)
2025-06-13 14:32:24 [main] DEBUG com.converter.ConverterApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-13 14:32:24 [main] INFO  com.converter.ConverterApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-13 14:32:25 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-13 14:32:25 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-13 14:32:25 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-13 14:32:25 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-13 14:32:25 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 711 ms
2025-06-13 14:32:25 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-13 14:32:25 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-13 14:32:25 [main] INFO  com.converter.ConverterApplication - Started ConverterApplication in 1.455 seconds (JVM running for 1.945)
2025-06-13 14:32:27 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-13 14:32:27 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-13 14:32:27 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-06-13 14:32:27 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 收到文本转换请求
2025-06-13 14:32:27 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 开始转换SQL Server存储过程到KingBase格式
2025-06-13 14:32:27 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 开始数据类型转换
2025-06-13 14:32:27 [http-nio-8080-exec-1] DEBUG c.c.converter.DataTypeConverter - 数据类型转换完成，共转换 0 个数据类型
2025-06-13 14:32:27 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 数据类型转换完成
2025-06-13 14:32:27 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 开始函数转换
2025-06-13 14:32:27 [http-nio-8080-exec-1] DEBUG c.c.converter.FunctionConverter - 函数转换完成，共转换 0 个函数
2025-06-13 14:32:27 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 函数转换完成
2025-06-13 14:32:27 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 开始语法结构转换
2025-06-13 14:32:27 [http-nio-8080-exec-1] DEBUG c.c.converter.SyntaxConverter - 语法结构转换完成，共转换 0 个语法结构
2025-06-13 14:32:27 [http-nio-8080-exec-1] DEBUG c.c.s.StoredProcedureConverterService - 语法结构转换完成
2025-06-13 14:32:27 [http-nio-8080-exec-1] INFO  c.c.s.StoredProcedureConverterService - 存储过程转换完成，状态: WARNING
2025-06-13 14:32:27 [http-nio-8080-exec-1] INFO  c.c.controller.ConversionController - 文本转换完成，状态: WARNING
